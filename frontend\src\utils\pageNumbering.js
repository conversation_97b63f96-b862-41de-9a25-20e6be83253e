import { useState, useEffect } from 'react';
import React from 'react';

// Section configuration with their identifiers and expected page counts
export const SECTION_CONFIG = [
  { 
    text: "Report Summary", 
    identifier: "Report Summary", 
    sectionKey: "reportSummary",
    expectedPages: 3 
  },
  { 
    text: "Current Fiscal Year", 
    identifier: "Current Fiscal Year", 
    sectionKey: "fiscalYear",
    expectedPages: 1 
  },
  { 
    text: "Expense Summary", 
    identifier: "Expense Summary", 
    sectionKey: "expenseSummary",
    expectedPages: 2 
  },
  { 
    text: "Operational Efficiency", 
    identifier: "Operational Efficiency", 
    sectionKey: "operationalEfficiency",
    expectedPages: 1 
  },
  { 
    text: "Liquidity Summary", 
    identifier: "Liquidity Summary", 
    sectionKey: "liquiditySummary",
    expectedPages: 1 
  },
  { 
    text: "Profit & Loss - 13 Month Trailing", 
    identifier: "13 Month Trailing", 
    sectionKey: "monthTrailing",
    expectedPages: 1 
  },
  { 
    text: "Profit & Loss - Monthly", 
    identifier: "Profit and Loss", 
    sectionKey: "monthly",
    expectedPages: 1 
  },
  { 
    text: "Profit & Loss - YTD", 
    identifier: "Year to Date", 
    sectionKey: "ytd",
    expectedPages: 1 
  },
  { 
    text: "Balance Sheet", 
    identifier: "Balance Sheet", 
    sectionKey: "balanceSheet",
    expectedPages: 1 
  }
];

// Page break div selectors (for future DOM-based calculation if needed)
// const PAGE_BREAK_SELECTORS = [
//   'div[class*="max-w-6xl"][class*="h-[420mm]"]',
//   'div[class*="max-w-6xl"][class*="h-[410mm]"]',
//   'div[class*="max-w-6xl"][class*="h-[400mm]"]',
//   'div[class*="max-w-8xl"][class*="mx-auto"][class*="bg-white"]'
// ];

/**
 * Calculate dynamic page numbers for table of contents
 * @returns {Object} Object with section keys mapped to their starting page numbers
 */
export const calculateDynamicPageNumbers = () => {
  const pageNumbers = {};
  let currentPageNumber = 1;

  // Calculate page numbers based on the expected structure
  // This is more reliable than DOM scanning since components may not be fully rendered
  SECTION_CONFIG.forEach((section) => {
    pageNumbers[section.sectionKey] = currentPageNumber;
    currentPageNumber += section.expectedPages;
  });

  console.log('Calculated page numbers:', pageNumbers);
  return pageNumbers;
};

/**
 * Custom hook for dynamic page numbering
 * @returns {Object} Object with section keys mapped to their starting page numbers
 */
export const useDynamicPageNumbers = () => {
  const [pageNumbers, setPageNumbers] = useState({});

  useEffect(() => {
    // Calculate page numbers on mount
    const newPageNumbers = calculateDynamicPageNumbers();
    setPageNumbers(newPageNumbers);
  }, []);

  return pageNumbers;
};

/**
 * Get table of contents items with dynamic page numbers
 * @param {Object} pageNumbers - Dynamic page numbers from useDynamicPageNumbers hook
 * @returns {Array} Array of TOC items with calculated page numbers
 */
export const getTocItemsWithDynamicPages = (pageNumbers) => {
  return SECTION_CONFIG.map(section => ({
    text: section.text,
    page: pageNumbers[section.sectionKey] || 1
  }));
};

/**
 * Page Number Component - displays page number at bottom of page div
 * @param {number} pageNumber - The page number to display
 * @param {Object} style - Optional style overrides
 */
export const PageNumber = ({ pageNumber, style = {} }) => {
  return (
    <div
      className="absolute bottom-4 right-6 text-gray-500 text-sm font-medium"
      style={{
        fontSize: '12px',
        color: '#6B7280',
        ...style
      }}
    >
      {pageNumber}
    </div>
  );
};

/**
 * Higher-order component to add page numbering to page divs
 * @param {React.Component} WrappedComponent - Component to wrap
 * @param {string} sectionKey - Section key for page number calculation
 */
export const withPageNumber = (WrappedComponent, sectionKey) => {
  return (props) => {
    const pageNumbers = useDynamicPageNumbers();
    const startingPage = pageNumbers[sectionKey] || 1;

    return <WrappedComponent {...props} startingPageNumber={startingPage} />;
  };
};
