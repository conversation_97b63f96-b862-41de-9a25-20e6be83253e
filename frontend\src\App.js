import React from "react";
import { createBrowserRouter, RouterProvider } from "react-router-dom";
import Auth from "./pages/Auth/Auth";
import Login from "./pages/Auth/Components/Login";
import ForgotPassword from "./pages/Auth/Components/ForgotPassword";
import CheckEmail from "./pages/Auth/Components/CheckEmail";
import ResetPassword from "./pages/Auth/Components/ResetPassword";
import Dashboard from "./pages/Dashboard/Dashboard";
import CompanyDetail from "./pages/Companies/Components/View";
import Examples from "./pages/examples/Examples";
import Layout from "./layout/LayOut";
import Faqs from "./pages/faqs/Faqs";
import EditReportPage from "./pages/reports/EditReportPage";
import ResetLinkPwd from "./pages/Auth/Components/ResetLink";
import QboCallback from "./pages/QboCallback/qboCallback";
// import Report from "./pages/Report/Report";
import SharedCompanies from "./pages/Companies/shared-companies/SharedCompanies"
import CustomizeReport from "./pages/reports/CustomizeReport";

const router = createBrowserRouter([
  {
    path: "/",
    element: <Auth />,
    children: [
      {
        index: true,
        element: <Login />,
      },
      {
        path: "login",
        element: <Login />,
      },
      {
        path: "forgot-password",
        element: <ForgotPassword />,
      },
      {
        path: "check-email",
        element: <CheckEmail />,
      },
      {
        path: "reset-password",
        element: <ResetPassword />,
      },
      {
        path: "reset-link",
        element: <ResetLinkPwd />,
      },
    ],
  },
  {
    path: "/",
    element: <Layout />,
    children: [
      {
        path: "dashboard",
        element: <Dashboard />,
      },
      {
        path: "companies",
        element: <Dashboard />,
      },
      {
        path: "share-companies",
        element: <SharedCompanies />,
      },
      {
        path: "company/:id",
        element: <CompanyDetail />,
      },
      {
        path: "qbo-callback",
        element: <QboCallback />,
      },
      {
        path: "examples",
        element: <Examples />,
      },
      {
        path: "faqs",
        element: <Faqs />,
      },

      {
        path: "companies/:companyId/reports/:reportId/edit",
        element: <EditReportPage />,
      },
      // {
      //   path: "report",
      //   element: <Report />,
      // },
      // 
    ],
  },
  {
    path: "/",
    children: [
     {
        path: "company/:id/custom-template/:reportId?",
        element: <CustomizeReport />,
      },
    ],
  },
]);

function App() {
  return <RouterProvider router={router} />;
}

export default App;
