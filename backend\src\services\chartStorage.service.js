// backend\src\services\chartStorage.service.js
import AWS from 'aws-sdk';
import { chromium } from 'playwright';
import dotenv from 'dotenv';
import { prisma } from '../db/prisma.js';

dotenv.config();

// Configure AWS SDK
AWS.config.update({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION
});

const s3 = new AWS.S3();

/**
 * Get company folder information for S3 organization
 * @param {number} companyId - Company ID
 * @returns {Promise<string>} - Folder name
 */
async function getCompanyFolderName(companyId) {
  if (!companyId) {
    return 'charts'; // Default folder if no company ID
  }

  try {
    const company = await prisma.company.findUnique({
      where: { id: parseInt(companyId) },
      select: { id: true, name: true }
    });

    if (!company) {
      return `charts/Unknown Company (Id: ${companyId})`;
    }

    const companyName = company.name || 'Unknown Company';
    return `charts/${companyName} (Id: ${company.id})`;
  } catch (error) {
    console.error('Error fetching company for folder name:', error);
    return `charts/Unknown Company (Id: ${companyId})`;
  }
}

/**
 * Save a chart to AWS S3 by rendering it with Playwright and uploading the screenshot
 * @param {Object} chartOptions - ApexCharts configuration object
 * @param {string} fileName - Name of the file to save (should include .png extension)
 * @param {boolean} mock - If true, will mock the upload instead of actually uploading
 * @param {number} companyId - Company ID for folder organization (optional)
 * @returns {Promise<string>} - Returns the S3 URL or mock URL
 */
export async function saveChartToS3(chartOptions, fileName, mock = false, companyId = null) {
  let browser;
  
  try {
    // 1️⃣ Render chart in Playwright
    console.log(`📊 Rendering chart: ${fileName}`);
    browser = await chromium.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Set viewport for consistent chart rendering
    await page.setViewportSize({ width: 1200, height: 800 });
    
    await page.setContent(`
      <html>
        <head>
          <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
          <style>
            body {
              margin: 0;
              padding: 20px;
              font-family: Arial, sans-serif;
              background: white;
            }
            #chart {
              width: 100%;
              height: 100%;
            }
          </style>
        </head>
        <body>
          <div id="chart"></div>
          <script>
            var options = ${JSON.stringify(chartOptions)};
            var chart = new ApexCharts(document.querySelector("#chart"), options);
            chart.render().then(() => {
              // Add a marker to indicate chart is ready
              document.body.setAttribute('data-chart-ready', 'true');
            });
          </script>
        </body>
      </html>
    `);

    // Wait for chart to be rendered
    await page.waitForSelector('#chart');
    await page.waitForFunction(() => document.body.getAttribute('data-chart-ready') === 'true', {
      timeout: 10000
    });

    // Take screenshot of the chart
    const chartElement = await page.$('#chart');
    if (!chartElement) {
      throw new Error('Chart element not found');
    }

    const buffer = await chartElement.screenshot({
      type: 'png',
      omitBackground: false
    });

    console.log(`📸 Chart screenshot captured: ${buffer.length} bytes`);

    // 2️⃣ Mock or real upload
    if (mock) {
      console.log(`🟡 MOCK UPLOAD: Would upload file "${fileName}" to bucket "${process.env.S3_BUCKET_NAME}"`);
      console.log(`File size: ${buffer.length} bytes`);
      return `mock://s3/${process.env.S3_BUCKET_NAME}/charts/${fileName}`;
    }

    // Validate required environment variables
    if (!process.env.S3_BUCKET_NAME) {
      throw new Error('S3_BUCKET_NAME environment variable is required');
    }

    // Get company folder path
    const folderPath = await getCompanyFolderName(companyId);

    // Upload to S3
    const params = {
      Bucket: process.env.S3_BUCKET_NAME,
      Key: `${folderPath}/${fileName}`,
      Body: buffer,
      ContentType: 'image/png',
      Metadata: {
        'generated-at': new Date().toISOString(),
        'chart-type': chartOptions.chart?.type || 'unknown',
        'company-id': companyId ? companyId.toString() : 'unknown'
      }
    };

    console.log(`⬆Uploading to S3: ${params.Bucket}/${folderPath}/${fileName}`);
    const uploadResult = await s3.upload(params).promise();
    
    console.log(`Chart uploaded to S3: ${uploadResult.Location}`);
    return uploadResult.Location;

  } catch (error) {
    console.error(`Error saving chart to S3:`, error);
    throw new Error(`Failed to save chart: ${error.message}`);
  } finally {
    // Always close the browser
    if (browser) {
      await browser.close();
    }
  }
}

/**
 * Save multiple charts to S3 in parallel
 * @param {Array} charts - Array of {chartOptions, fileName} objects
 * @param {boolean} mock - If true, will mock the uploads
 * @returns {Promise<Array>} - Returns array of S3 URLs
 */
export async function saveMultipleChartsToS3(charts, mock = false) {
  console.log(`Saving ${charts.length} charts to S3...`);
  
  try {
    const uploadPromises = charts.map(({ chartOptions, fileName }) => 
      saveChartToS3(chartOptions, fileName, mock)
    );

    const results = await Promise.all(uploadPromises);
    console.log(`Successfully saved ${results.length} charts`);
    return results;
  } catch (error) {
    console.error(`Error saving multiple charts:`, error);
    throw error;
  }
}

/**
 * Delete a chart from S3
 * @param {string} fileName - Name of the file to delete
 * @returns {Promise<boolean>} - Returns true if successful
 */
export async function deleteChartFromS3(fileName) {
  try {
    if (!process.env.S3_BUCKET_NAME) {
      throw new Error('S3_BUCKET_NAME environment variable is required');
    }

    const params = {
      Bucket: process.env.S3_BUCKET_NAME,
      Key: `charts/${fileName}`
    };

    await s3.deleteObject(params).promise();
    console.log(`🗑️ Chart deleted from S3: ${fileName}`);
    return true;
  } catch (error) {
    console.error(`❌ Error deleting chart from S3:`, error);
    throw new Error(`Failed to delete chart: ${error.message}`);
  }
}

/**
 * Generate a unique filename for a chart based on database schema
 * @param {string} chartUniqueName - Unique name/type of chart (e.g., ROA_ROE_Chart)
 * @param {number} companyId - Company ID from database
 * @param {number} reportId - Report request ID from database
 * @param {string|Date} dateRequested - Date when report was requested
 * @returns {string} - Filename in format: CID_companyId__chartUniqueName_RID_reportId_dateRequested.png
 */
export function generateChartFileName(chartUniqueName, companyId, reportId, dateRequested) {
  // Convert dateRequested to a safe filename format
  let dateStr;
  if (dateRequested instanceof Date) {
    dateStr = dateRequested.toISOString().split('T')[0]; // YYYY-MM-DD
  } else if (typeof dateRequested === 'string') {
    dateStr = new Date(dateRequested).toISOString().split('T')[0];
  } else {
    dateStr = new Date().toISOString().split('T')[0]; // fallback to today
  }

  return `CID_${companyId}__${chartUniqueName}_RID_${reportId}_${dateStr}.png`;
}

// Example usage and testing function
export async function testChartStorage() {
  const expensesPieOptions = {
    series: [1200, 800, 500],
    chart: { 
      type: "pie", 
      height: 400, 
      toolbar: { show: false },
      background: 'transparent'
    },
    labels: ["Rent", "Utilities", "Misc"],
    colors: ["#FF4560", "#00E396", "#FEB019"],
    dataLabels: { 
      enabled: true,
      style: {
        fontSize: '14px',
        fontWeight: 'bold'
      }
    },
    legend: {
      position: 'bottom'
    },
    title: {
      text: 'Expense Breakdown',
      align: 'center',
      style: {
        fontSize: '18px',
        fontWeight: 'bold'
      }
    }
  };

  try {
    // Test with mock upload
    const mockUrl = await saveChartToS3(expensesPieOptions, 'test_expenses_pie.png', true);
    console.log('Mock upload successful:', mockUrl);

    // Uncomment below to test real upload
    // const realUrl = await saveChartToS3(expensesPieOptions, 'test_expenses_pie.png', false);
    // console.log('Real upload successful:', realUrl);

    return mockUrl;
  } catch (error) {
    console.error('Test failed:', error);
    throw error;
  }
}

// Export default for convenience
export default {
  saveChartToS3,
  saveMultipleChartsToS3,
  deleteChartFromS3,
  generateChartFileName,
  testChartStorage
};