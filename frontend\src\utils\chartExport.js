/**
 * Utility functions for exporting ApexCharts as PNG images
 */

/**
 * Export a specific ApexChart as PNG
 * @param {string} chartId - The ID of the chart container or chart instance
 * @param {string} filename - The filename for the downloaded PNG (without extension)
 * @param {Object} options - Export options
 * @returns {Promise<boolean>} - Returns true if export was successful
 */
export const exportChartAsPNG = async (chartId, filename = 'chart', options = {}) => {
  try {
    // Default options
    const defaultOptions = {
      type: 'png',
      width: 800,
      height: 400,
      ...options
    };

    // Try multiple ways to access ApexCharts
    let ApexChartsLib = null;

    // Method 1: Check if ApexCharts is available globally
    if (window.ApexCharts) {
      ApexChartsLib = window.ApexCharts;
    }
    // Method 2: Try to import ApexCharts dynamically
    else {
      try {
        const ApexChartsModule = await import('apexcharts');
        ApexChartsLib = ApexChartsModule.default || ApexChartsModule;
      } catch (importError) {
        throw new Error('ApexCharts library not found');
      }
    }

    if (!ApexChartsLib) {
      throw new Error('ApexCharts library not accessible');
    }

    // Get chart instance
    let chart;

    // Try to get chart by ID first
    chart = ApexChartsLib.getChartByID(chartId);

    // If not found, try to find by container ID
    if (!chart) {
      const container = document.getElementById(chartId);
      if (container) {
        const apexContainer = container.querySelector('div[id*="apex"]');
        if (apexContainer) {
          chart = ApexChartsLib.getChartByID(apexContainer.id);
        }
      }
    }

    if (!chart) {
      throw new Error(`Chart with ID "${chartId}" not found`);
    }

    // Generate image data URI
    const uri = await chart.dataURI(defaultOptions);

    if (!uri || !uri.imgURI) {
      throw new Error('Failed to generate chart image data');
    }

    // Create download link
    const link = document.createElement('a');
    link.href = uri.imgURI;
    link.download = `${filename}.png`;
    link.style.display = 'none';

    // Trigger download
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    return true;
  } catch (error) {
    console.error('Chart export error:', error);
    throw error;
  }
};

/**
 * Export ROA and ROE chart specifically
 * @param {string} filename - Optional filename (defaults to ROA_ROE_Chart_YYYY-MM-DD)
 * @returns {Promise<boolean>} - Returns true if export was successful
 */
export const exportROAROEChart = async (filename) => {
  const defaultFilename = `ROA_ROE_Chart_${new Date().toISOString().split('T')[0]}`;
  
  try {
    // Find the ROA and ROE chart section
    const roaRoeSection = document.getElementById('roaAndRoe');
    if (!roaRoeSection) {
      throw new Error('ROA and ROE chart section not found');
    }

    // Find the chart container
    const chartContainer = roaRoeSection.querySelector('div[id*="apex"]');
    if (!chartContainer) {
      throw new Error('Chart container not found');
    }

    return await exportChartAsPNG(chartContainer.id, filename || defaultFilename, {
      width: 800,
      height: 400
    });
  } catch (error) {
    console.error('ROA/ROE chart export error:', error);
    throw error;
  }
};

/**
 * Get all available charts on the page
 * @returns {Array} - Array of chart information objects
 */
export const getAvailableCharts = () => {
  const charts = [];
  
  // Find all ApexCharts containers
  const chartContainers = document.querySelectorAll('div[id*="apex"]');
  
  chartContainers.forEach(container => {
    const chart = window.ApexCharts?.getChartByID(container.id);
    if (chart) {
      // Try to find the section this chart belongs to
      const section = container.closest('[id]');
      const sectionId = section?.id || 'unknown';
      
      charts.push({
        chartId: container.id,
        sectionId: sectionId,
        chartType: chart.w?.config?.chart?.type || 'unknown',
        isRendered: true
      });
    }
  });
  
  return charts;
};

/**
 * Export multiple charts as PNG files
 * @param {Array} chartIds - Array of chart IDs to export
 * @param {string} prefix - Filename prefix for all charts
 * @returns {Promise<Array>} - Array of export results
 */
export const exportMultipleCharts = async (chartIds, prefix = 'chart') => {
  const results = [];
  
  for (let i = 0; i < chartIds.length; i++) {
    const chartId = chartIds[i];
    try {
      const filename = `${prefix}_${i + 1}_${new Date().toISOString().split('T')[0]}`;
      await exportChartAsPNG(chartId, filename);
      results.push({ chartId, success: true, filename });
    } catch (error) {
      results.push({ chartId, success: false, error: error.message });
    }
  }
  
  return results;
};
