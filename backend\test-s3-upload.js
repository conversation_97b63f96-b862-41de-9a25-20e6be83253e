// Test S3 upload without <PERSON><PERSON> with company folder structure
import AWS from 'aws-sdk';
import dotenv from 'dotenv';
import { prisma } from './src/db/prisma.js';

dotenv.config();

// Configure AWS SDK
AWS.config.update({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION
});

const s3 = new AWS.S3();

async function testS3Upload() {
  try {
    console.log('🧪 Testing S3 upload with company folder structure...');

    // Create a simple test image buffer (1x1 pixel PNG)
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
      0x01, 0x00, 0x01, 0x5C, 0xC2, 0x5D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
      0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);

    // Test the new naming convention: companyId_ReportId_chartUniqueName_dateRequested
    const companyId = 11; // Use a real company ID that might exist
    const reportId = 456;
    const chartUniqueName = 'test-chart';
    const dateRequested = new Date();
    const dateStr = dateRequested.toISOString().split('T')[0]; // YYYY-MM-DD
    const fileName = `${companyId}_${reportId}_${chartUniqueName}_${dateStr}.png`;

    console.log(`📝 Testing new naming convention: ${fileName}`);

    // Test company folder creation
    let folderName = 'charts'; // Default
    try {
      const company = await prisma.company.findUnique({
        where: { id: companyId },
        select: { id: true, name: true }
      });

      if (company) {
        const companyName = company.name || 'Unknown Company';
        folderName = `charts/${companyName} (Id: ${company.id})`;
        console.log(`📁 Company found: ${company.name} (ID: ${company.id})`);
        console.log(`📁 Folder structure: ${folderName}`);
      } else {
        folderName = `charts/Unknown Company (Id: ${companyId})`;
        console.log(`📁 Company not found, using fallback folder: ${folderName}`);
      }
    } catch (dbError) {
      console.log(`📁 Database error, using default folder: ${dbError.message}`);
      folderName = `charts/Unknown Company (Id: ${companyId})`;
    }
    
    // Upload parameters WITHOUT ACL but WITH company folder
    const params = {
      Bucket: process.env.S3_BUCKET_NAME,
      Key: `${folderName}/${fileName}`,
      Body: testImageBuffer,
      ContentType: 'image/png',
      Metadata: {
        'generated-at': new Date().toISOString(),
        'test-upload': 'true',
        'company-id': companyId.toString()
      }
    };

    console.log(`⬆️ Uploading test file: ${params.Bucket}/${folderName}/${fileName}`);
    
    const uploadResult = await s3.upload(params).promise();
    
    console.log(`✅ Test upload successful!`);
    console.log(`📍 S3 URL: ${uploadResult.Location}`);
    console.log(`🔑 ETag: ${uploadResult.ETag}`);
    
    // Test if we can access the uploaded file
    console.log('\n🔍 Testing file access...');
    const headParams = {
      Bucket: process.env.S3_BUCKET_NAME,
      Key: `${folderName}/${fileName}`
    };

    const headResult = await s3.headObject(headParams).promise();
    console.log(`✅ File exists and is accessible`);
    console.log(`📊 File size: ${headResult.ContentLength} bytes`);
    console.log(`📅 Last modified: ${headResult.LastModified}`);
    console.log(`📁 Full S3 path: ${folderName}/${fileName}`);

    // Clean up test file
    console.log('\n🧹 Cleaning up test file...');
    const deleteParams = {
      Bucket: process.env.S3_BUCKET_NAME,
      Key: `${folderName}/${fileName}`
    };
    
    await s3.deleteObject(deleteParams).promise();
    console.log(`✅ Test file deleted successfully`);
    
    console.log('\n🎉 S3 upload test completed successfully!');
    console.log('✅ ACL issue has been resolved');
    console.log('✅ Charts can now be uploaded to S3 without errors');
    
  } catch (error) {
    console.error('❌ S3 upload test failed:', error);
    
    if (error.code === 'AccessControlListNotSupported') {
      console.log('\n💡 ACL Error detected - this should be fixed now');
      console.log('🔧 Make sure ACL parameters are removed from upload code');
    } else if (error.code === 'NoSuchBucket') {
      console.log('\n💡 Bucket not found - check bucket name and region');
    } else if (error.code === 'InvalidAccessKeyId') {
      console.log('\n💡 Invalid AWS credentials - check .env file');
    } else {
      console.log('\n💡 Other error - check AWS permissions and configuration');
    }
  }
}

// Run the test
testS3Upload();
