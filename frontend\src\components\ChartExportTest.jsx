import React, { useEffect, useRef } from 'react';
import ApexCharts from 'apexcharts';
import { Button } from '@mui/material';
import { exportChartAsPNG } from '../utils/chartExport';

// Ensure ApexCharts is available globally
if (!window.ApexCharts) {
  window.ApexCharts = ApexCharts;
}

/**
 * Test component to verify chart export functionality
 */
const ChartExportTest = () => {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  useEffect(() => {
    if (chartRef.current) {
      // Sample chart configuration
      const options = {
        series: [
          {
            name: "ROA",
            data: [2.5, 3.1, 2.8, 3.5, 4.2, 3.9]
          },
          {
            name: "ROE",
            data: [8.2, 9.1, 8.5, 9.8, 11.2, 10.5]
          }
        ],
        chart: {
          type: "line",
          height: 300,
          toolbar: { show: false },
          background: "transparent",
        },
        dataLabels: {
          enabled: true,
          formatter: function (val) {
            if (val === null || val === undefined || isNaN(val)) return "0";
            return val + "%";
          },
          style: {
            fontSize: "14px",
            colors: ["#333"],
            fontWeight: "500",
          },
          background: {
            enabled: false,
          },
          offsetY: -5,
        },
        stroke: {
          curve: "smooth",
          width: 2,
        },
        xaxis: {
          categories: ['2019', '2020', '2021', '2022', '2023', '2024'],
          labels: {
            style: {
              colors: "#666",
              fontSize: "14px",
            },
          },
          axisBorder: {
            show: false,
          },
          axisTicks: {
            show: false,
          },
        },
        yaxis: {
          show: false,
        },
        colors: ["#20b2aa", "#ff6b6b"],
        legend: {
          position: "bottom",
          horizontalAlign: "center",
          fontSize: "14px",
          fontWeight: 500,
          markers: {
            width: 12,
            height: 12,
            radius: 6,
          },
          itemMargin: {
            horizontal: 20,
            vertical: 5,
          },
        },
        grid: {
          show: false,
        },
        tooltip: {
          enabled: true,
          y: {
            formatter: function (val) {
              return val + "%";
            },
          },
        },
      };

      // Create chart
      chartInstance.current = new ApexCharts(chartRef.current, options);
      chartInstance.current.render();
    }

    // Cleanup
    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, []);

  const handleExportTest = async () => {
    try {
      if (chartRef.current) {
        const chartContainer = chartRef.current.querySelector('div[id*="apex"]');
        if (chartContainer) {
          await exportChartAsPNG(chartContainer.id, 'test_chart');
          alert('Chart exported successfully!');
        } else {
          alert('Chart container not found');
        }
      }
    } catch (error) {
      console.error('Export test error:', error);
      alert(`Export failed: ${error.message}`);
    }
  };

  const handleExportWithApexCharts = async () => {
    try {
      // Try to access ApexCharts directly
      let ApexChartsLib = null;

      if (window.ApexCharts) {
        ApexChartsLib = window.ApexCharts;
      } else {
        // Import ApexCharts dynamically
        const ApexChartsModule = await import('apexcharts');
        ApexChartsLib = ApexChartsModule.default || ApexChartsModule;

        // Attach to window for future use
        window.ApexCharts = ApexChartsLib;
      }

      if (chartRef.current) {
        const chartContainer = chartRef.current.querySelector('div[id*="apex"]');
        if (chartContainer) {
          const chart = ApexChartsLib.getChartByID(chartContainer.id);
          if (chart) {
            const uri = await chart.dataURI({
              type: 'png',
              width: 800,
              height: 400
            });

            const link = document.createElement('a');
            link.href = uri.imgURI;
            link.download = `test_chart_apexcharts_${new Date().toISOString().split('T')[0]}.png`;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            alert('ApexCharts export successful!');
          } else {
            alert('Chart instance not found');
          }
        } else {
          alert('Chart container not found');
        }
      }
    } catch (error) {
      console.error('ApexCharts export error:', error);
      alert(`ApexCharts export failed: ${error.message}`);
    }
  };

  const handleDirectExport = async () => {
    try {
      if (chartInstance.current) {
        const uri = await chartInstance.current.dataURI({
          type: 'png',
          width: 800,
          height: 400
        });

        const link = document.createElement('a');
        link.href = uri.imgURI;
        link.download = `test_chart_direct_${new Date().toISOString().split('T')[0]}.png`;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert('Direct export successful!');
      }
    } catch (error) {
      alert(`Direct export failed: ${error.message}`);
    }
  };



  return (
    <div style={{ padding: '20px' }}>
      <h2>Chart Export Test</h2>
      <div 
        ref={chartRef} 
        id="testChart"
        style={{ 
          width: '100%', 
          height: '300px', 
          border: '1px solid #ccc',
          marginBottom: '20px'
        }}
      ></div>
      
      <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
        <Button
          variant="contained"
          onClick={handleExportTest}
          color="primary"
        >
          Export via Utility Function
        </Button>

        <Button
          variant="contained"
          onClick={handleDirectExport}
          color="secondary"
        >
          Direct Export (Instance)
        </Button>

        <Button
          variant="contained"
          onClick={handleExportWithApexCharts}
          color="success"
        >
          Export with ApexCharts Access
        </Button>
      </div>
      
      <div style={{ marginTop: '20px', fontSize: '14px', color: '#666' }}>
        <p><strong>Instructions:</strong></p>
        <ul>
          <li>The chart above should render automatically</li>
          <li><strong>Export via Utility Function:</strong> Tests the utility function with dynamic import fallback</li>
          <li><strong>Direct Export (Instance):</strong> Uses the stored chart instance directly</li>
          <li><strong>Export with ApexCharts Access:</strong> Tests ApexCharts access and attaches to window</li>
          <li>All methods should download a PNG file of the chart</li>
          <li>If one method fails, try the others to identify the issue</li>
        </ul>

        <p><strong>Troubleshooting:</strong></p>
        <ul>
          <li>If "ApexCharts library not found" error occurs, try the "Export with ApexCharts Access" button first</li>
          <li>Check browser console for detailed error messages</li>
          <li>Ensure the chart is fully rendered before attempting export</li>
        </ul>
      </div>
    </div>
  );
};

export default ChartExportTest;
