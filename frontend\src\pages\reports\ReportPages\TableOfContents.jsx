import React from 'react';

const TableOfContents = ({ 
  headingTextStyle = {},
  contentTextStyle = {},
  headerTextStyle = {},
  subHeadingTextStyle = {},
  reportData = null
}) => {
  // Table of Contents data directly in component
  const tocItems = [
    { text: "Report Summary", page: 1 },
    { text: "Current Fiscal Year", page: 4 },
    { text: "Expense Summary", page: 6 },
    { text: "Operational Efficiency", page: 8 },
    { text: "Liquidity Summary", page: 10 },
    { text: "Profit & Loss - 13 Month Trailing", page: 11 },
    { text: "Profit & Loss - Monthly", page: 15 },
    { text: "Profit & Loss - YTD", page: 18 },
    { text: "Balance Sheet", page: 20 }
  ];

  const formatHeaderPeriod = (startYear, startMonth) => {
    const monthNames = [
      "January", "February", "March", "April", "May", "June",
      "July", "August", "September", "October", "November", "December"
    ];

    if (!startYear || !startMonth) {
      return " "; // fallback
    }

    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index

    return `${startMonthName} ${startYear}`;
  };

  return (
    <div className="p-5">

      {/* Main Container */}
      <div className="max-w-6xl h-[400mm] mx-auto bg-white flex flex-col min-h-screen p-10  h-[297mm]">

         {/* Header Section */}
    <div className="component-header flex items-center justify-between gap-4 mb-8 border-b-4 border-blue-900 pb-2">
          <h1
            className="text-4xl font-bold text-gray-800 m-0"
            style={headerTextStyle}
          >
          </h1>
          <p className="text-lg text-gray-600 m-0" style={subHeadingTextStyle}>
            {formatHeaderPeriod(reportData?.FYStartYear, reportData?.FYStartMonth)} | Acme Print
          </p>
        </div>
        
        <div className='flex flex-col justify-center h-full'>

        {/* Header Section */}
        <div className="text-center flex justify-center   pb-6">
          <h1 
            className="text-5xl font-light text-gray-800 m-0"
            style={headingTextStyle}
          >
            Table of Contents
          </h1>
        </div>

       {/* Table of Contents Items */}
        <div className="max-w-2xl mx-auto w-full px-20 space-y-1">
          {tocItems.map((item, index) => (
            <div 
              key={index}
              className={`
                flex items-baseline	 py-2 px-2
                ${index === tocItems.length - 1 ? '' : ''}
              `}
            >
              <span 
                className="text-lg text-gray-700 font-medium flex-shrink-0"
                style={contentTextStyle}
              >
                {item.text}
              </span>
              <div className="flex-1 mx-4 border-b-[2.5px] border-dotted border-black min-w-4"></div>
              <span 
                className="text-lg text-teal-600 font-semibold flex-shrink-0"
                style={contentTextStyle}
              >
                {item.page}
              </span>
            </div>
          ))}
        </div>
        
        </div>

      </div>
    </div>
  );
};

export default TableOfContents;