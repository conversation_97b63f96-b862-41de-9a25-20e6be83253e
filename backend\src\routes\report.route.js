
import { Router } from 'express';
export const reportRoute = Router();
import { authenticate } from '../middleware/auth.middleware.js';
import * as reportController from '../controllers/report.controller.js';
import path from 'path';
import multer from 'multer';

// Set up multer memory storage
const storage = multer.memoryStorage();

const upload = multer({
    storage,
    fileFilter: (req, file, cb) => {
        const allowedExtensions = /jpeg|jpg|png|pdf|csv|xlsx/;
        const allowedMimeTypes = [
            "image/jpeg",
            "image/jpg",
            "image/png",
            "application/pdf",
            "text/csv",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        ];

        const extname = allowedExtensions.test(path.extname(file.originalname).toLowerCase());
        const mimetype = allowedMimeTypes.includes(file.mimetype);

        console.log("file.mimetype -----", file.mimetype);
        console.log("mime type -----", mimetype, extname);
        
        if (mimetype && extname) {
            cb(null, true);
        } else {
            cb(new Error('File type not supported.'));
        }
    },
    limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
})


const uploads = multer();


reportRoute.post('/', authenticate, reportController.registerRequest);
reportRoute.get('/:id/generate-calculation/:reportRequestId', authenticate, reportController.generateReportCalculations);
// reportRoute.post('/:id/generate-pdf', authenticate, reportController.generateAndStorePDF);
reportRoute.get('/:id', authenticate, reportController.getReportById)
reportRoute.post('/:id/editable', authenticate, reportController.saveEditedReportRequest);
reportRoute.get('/:id/all', authenticate, reportController.getAllReportRequests);
reportRoute.post('/:id/cashflow/upload', upload.single('file'), reportController.uploadFile)
reportRoute.post('/:id/non-cashflow/upload', upload.single('file'), reportController.nonCashflowUploadFile)
reportRoute.post('/edited-file/:id/upload', upload.single('file'), reportController.uploadEditedFileForNonCashFlow)
reportRoute.get('/download', reportController.downloadFile);
reportRoute.delete('/:id', authenticate, reportController.deleteRequest)
reportRoute.get('/generate-report-link/:key', reportController.generateReportLink);
reportRoute.post("/:id/save-text",uploads.none(),reportController.saveText)
